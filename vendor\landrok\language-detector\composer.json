{"name": "landrok/language-detector", "type": "library", "version": "1.4.0", "description": "A fast and reliable PHP library for detecting languages", "keywords": ["language", "detector", "n-grams"], "homepage": "https://github.com/landrok/language-detector", "license": "MIT", "require": {"php": ">=7.4", "ext-mbstring": "*", "webmozart/assert": "^1.2"}, "require-dev": {"phpunit/phpunit": ">=6"}, "autoload": {"psr-4": {"LanguageDetector\\": "src/LanguageDetector/"}}, "autoload-dev": {"psr-4": {"LanguageDetectorTest\\": "tests/LanguageDetector/"}}}