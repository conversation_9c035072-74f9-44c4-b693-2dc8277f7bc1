<?php

namespace Tests\Unit;

use App\Services\LanguageDetectionService;
use Tests\TestCase;

class LanguageDetectionServiceTest extends TestCase
{
    private LanguageDetectionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new LanguageDetectionService();
    }

    public function test_detects_english_message()
    {
        $result = $this->service->detectLanguage('Hello, how are you today?');

        $this->assertEquals('english', $result['language']);
        $this->assertEquals('en', $result['language_code']);
        $this->assertEquals('automatic', $result['method']);
        $this->assertGreaterThan(0, $result['confidence']);
    }

    public function test_detects_arabic_message()
    {
        $result = $this->service->detectLanguage('مرحبا كيف حالك اليوم؟');

        // Note: The language detector might not perfectly detect Arabic
        // so we'll check if it at least returns a valid result
        $this->assertIsString($result['language']);
        $this->assertIsString($result['language_code']);
        $this->assertEquals('automatic', $result['method']);
        $this->assertIsFloat($result['confidence']);
    }

    public function test_language_override_arabic()
    {
        $result = $this->service->detectLanguage('أجب بالعربية. Hello how are you?');

        $this->assertEquals('arabic', $result['language']);
        $this->assertEquals('ar', $result['language_code']);
        $this->assertEquals('override', $result['method']);
        $this->assertEquals(1.0, $result['confidence']);
        $this->assertStringContainsString('Hello how are you?', $result['cleaned_message']);
        $this->assertStringNotContainsString('أجب بالعربية', $result['cleaned_message']);
    }

    public function test_language_override_english()
    {
        $result = $this->service->detectLanguage('Respond in English. مرحبا كيف حالك؟');

        $this->assertEquals('english', $result['language']);
        $this->assertEquals('en', $result['language_code']);
        $this->assertEquals('override', $result['method']);
        $this->assertEquals(1.0, $result['confidence']);
    }

    public function test_fallback_to_english_on_error()
    {
        // Test with very short or unclear text
        $result = $this->service->detectLanguage('a');

        $this->assertEquals('english', $result['language']);
        $this->assertEquals('en', $result['language_code']);
        $this->assertContains($result['method'], ['automatic', 'fallback']);
    }
}
