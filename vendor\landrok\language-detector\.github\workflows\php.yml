name: <PERSON>HP Composer

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  integration:
    name: PHP ${{ matrix.php-versions }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-versions: ['7.4', '8.0', '8.1', '8.2', '8.3']

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: "${{ matrix.php-versions }}"
        coverage: xdebug
        ini-values: "memory_limit=-1"
        tools: phpunit, composer

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v2
      with:
        path: vendor
        key: ${{ runner.os }}-${{ matrix.php-versions }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.php-versions }}-php-

    - name: Install dependencies
      run: composer update --no-progress --no-suggest --ansi

    - name: Run test suite
      run: |
        php -v
        vendor/bin/phpunit -c phpunit.xml.dist
