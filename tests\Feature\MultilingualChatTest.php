<?php

namespace Tests\Feature;

use App\Models\ChatSession;
use App\Models\Message;
use App\Services\LanguageDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MultilingualChatTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations and seeders
        $this->artisan('migrate');
        $this->artisan('db:seed', ['--class' => 'PersonalitySeeder']);
    }

    public function test_chat_detects_english_and_responds_appropriately()
    {
        $response = $this->postJson('/api/chat', [
            'message' => 'Hello WIDDX, how are you today?',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'session_id',
                    'personality',
                    'processing_time',
                    'timestamp'
                ]);

        // Check that the message was stored with language information
        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $this->assertNotNull($session);

        $userMessage = $session->messages()->where('role', 'user')->first();
        $this->assertNotNull($userMessage);
        $this->assertEquals('english', $userMessage->detected_language);
        $this->assertEquals('en', $userMessage->detected_language_code);
        $this->assertEquals('automatic', $userMessage->language_detection_method);
    }

    public function test_chat_detects_spanish_and_responds_appropriately()
    {
        $response = $this->postJson('/api/chat', [
            'message' => 'Hola WIDDX, ¿cómo estás hoy?',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200);

        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $userMessage = $session->messages()->where('role', 'user')->first();

        // Language detection might not be perfect, so we'll be flexible
        $this->assertNotNull($userMessage->detected_language);
        $this->assertNotNull($userMessage->detected_language_code);
        $this->assertEquals('automatic', $userMessage->language_detection_method);
    }

    public function test_chat_detects_arabic_and_responds_appropriately()
    {
        $response = $this->postJson('/api/chat', [
            'message' => 'مرحبا WIDDX، كيف حالك اليوم؟',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200);

        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $userMessage = $session->messages()->where('role', 'user')->first();

        // Check that language detection worked
        $this->assertNotNull($userMessage->detected_language);
        $this->assertNotNull($userMessage->detected_language_code);
        $this->assertEquals('automatic', $userMessage->language_detection_method);
        $this->assertIsFloat($userMessage->language_confidence);
    }

    public function test_chat_handles_language_override()
    {
        $response = $this->postJson('/api/chat', [
            'message' => 'Respond in English. Hola, ¿cómo estás?',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200);

        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $userMessage = $session->messages()->where('role', 'user')->first();

        $this->assertEquals('english', $userMessage->detected_language);
        $this->assertEquals('en', $userMessage->detected_language_code);
        $this->assertEquals('override', $userMessage->language_detection_method);
        $this->assertTrue($userMessage->language_override_used);

        // Check that the cleaned message doesn't contain the override command
        $this->assertStringNotContainsString('Respond in English', $userMessage->content);
        $this->assertStringContainsString('Hola', $userMessage->content);
    }

    public function test_chat_handles_arabic_language_override()
    {
        $response = $this->postJson('/api/chat', [
            'message' => 'أجب بالعربية. Hello, how are you today?',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200);

        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $userMessage = $session->messages()->where('role', 'user')->first();

        $this->assertEquals('arabic', $userMessage->detected_language);
        $this->assertEquals('ar', $userMessage->detected_language_code);
        $this->assertEquals('override', $userMessage->language_detection_method);
        $this->assertTrue($userMessage->language_override_used);

        // Check that the cleaned message doesn't contain the override command
        $this->assertStringNotContainsString('أجب بالعربية', $userMessage->content);
        $this->assertStringContainsString('Hello', $userMessage->content);
    }

    public function test_session_language_preferences_are_updated()
    {
        // First message in Spanish
        $response1 = $this->postJson('/api/chat', [
            'message' => 'Hola WIDDX, ¿cómo estás?',
            'personality' => 'neutral'
        ]);

        $sessionId = $response1->json('session_id');

        // Second message in Spanish
        $response2 = $this->postJson('/api/chat', [
            'message' => '¿Puedes ayudarme con algo?',
            'personality' => 'neutral',
            'session_id' => $sessionId
        ]);

        $response2->assertStatus(200);

        $session = ChatSession::where('session_id', $sessionId)->first();

        // Check that language history is being tracked
        $this->assertNotNull($session->language_history);
        $this->assertArrayHasKey('spanish', $session->language_history);
        $this->assertGreaterThanOrEqual(2, $session->language_history['spanish']['count']);
    }

    public function test_personality_consistency_across_languages()
    {
        $personalities = ['witty', 'formal', 'casual', 'sarcastic'];
        $messages = [
            'en' => 'Hello WIDDX, tell me a joke.',
            'es' => 'Hola WIDDX, cuéntame un chiste.',
            'fr' => 'Bonjour WIDDX, raconte-moi une blague.',
        ];

        foreach ($personalities as $personality) {
            foreach ($messages as $langCode => $message) {
                $response = $this->postJson('/api/chat', [
                    'message' => $message,
                    'personality' => $personality
                ]);

                $response->assertStatus(200);

                $session = ChatSession::where('session_id', $response->json('session_id'))->first();
                $widdxMessage = $session->messages()->where('role', 'widdx')->first();

                $this->assertEquals($personality, $widdxMessage->personality_applied);
                $this->assertNotEmpty($response->json('message'));
            }
        }
    }

    public function test_conversation_history_maintains_language_context()
    {
        // Start conversation in Spanish
        $response1 = $this->postJson('/api/chat', [
            'message' => 'Hola WIDDX, me llamo Juan.',
            'personality' => 'neutral'
        ]);

        $sessionId = $response1->json('session_id');

        // Continue conversation in Spanish
        $response2 = $this->postJson('/api/chat', [
            'message' => '¿Recuerdas mi nombre?',
            'personality' => 'neutral',
            'session_id' => $sessionId
        ]);

        $response2->assertStatus(200);

        $session = ChatSession::where('session_id', $sessionId)->first();
        $messages = $session->messages()->orderBy('created_at')->get();

        $this->assertCount(4, $messages); // 2 user + 2 WIDDX messages

        // Check that both user messages were detected as Spanish
        $userMessages = $messages->where('role', 'user');
        foreach ($userMessages as $message) {
            $this->assertEquals('spanish', $message->detected_language);
        }
    }

    public function test_mixed_language_conversation()
    {
        // Start in English
        $response1 = $this->postJson('/api/chat', [
            'message' => 'Hello WIDDX, I speak multiple languages.',
            'personality' => 'neutral'
        ]);

        $sessionId = $response1->json('session_id');

        // Switch to Spanish
        $response2 = $this->postJson('/api/chat', [
            'message' => 'Ahora voy a hablar en español.',
            'personality' => 'neutral',
            'session_id' => $sessionId
        ]);

        // Switch to French
        $response3 = $this->postJson('/api/chat', [
            'message' => 'Maintenant je parle français.',
            'personality' => 'neutral',
            'session_id' => $sessionId
        ]);

        $response3->assertStatus(200);

        $session = ChatSession::where('session_id', $sessionId)->first();
        $userMessages = $session->messages()->where('role', 'user')->orderBy('created_at')->get();

        $this->assertEquals('english', $userMessages[0]->detected_language);
        $this->assertEquals('spanish', $userMessages[1]->detected_language);
        $this->assertEquals('french', $userMessages[2]->detected_language);

        // Check language history tracks all languages
        $languageHistory = $session->language_history;
        $this->assertArrayHasKey('english', $languageHistory);
        $this->assertArrayHasKey('spanish', $languageHistory);
        $this->assertArrayHasKey('french', $languageHistory);
    }

    public function test_language_override_with_different_personalities()
    {
        $testCases = [
            ['personality' => 'witty', 'override' => 'respond in english'],
            ['personality' => 'formal', 'override' => 'répondez en français'],
            ['personality' => 'casual', 'override' => 'responde en español'],
        ];

        foreach ($testCases as $testCase) {
            $response = $this->postJson('/api/chat', [
                'message' => $testCase['override'] . '. How are you doing today?',
                'personality' => $testCase['personality']
            ]);

            $response->assertStatus(200);

            $session = ChatSession::where('session_id', $response->json('session_id'))->first();
            $userMessage = $session->messages()->where('role', 'user')->first();
            $widdxMessage = $session->messages()->where('role', 'widdx')->first();

            $this->assertEquals('override', $userMessage->language_detection_method);
            $this->assertTrue($userMessage->language_override_used);
            $this->assertEquals($testCase['personality'], $widdxMessage->personality_applied);
        }
    }

    public function test_error_handling_with_invalid_input()
    {
        // Test with empty message
        $response = $this->postJson('/api/chat', [
            'message' => '',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(400);

        // Test with very long message
        $longMessage = str_repeat('This is a very long message. ', 1000);
        $response = $this->postJson('/api/chat', [
            'message' => $longMessage,
            'personality' => 'neutral'
        ]);

        // Should either succeed or fail gracefully
        $this->assertContains($response->status(), [200, 400, 413]);
    }

    public function test_language_confidence_affects_processing()
    {
        // Test with ambiguous short message
        $response = $this->postJson('/api/chat', [
            'message' => 'Hi',
            'personality' => 'neutral'
        ]);

        $response->assertStatus(200);

        $session = ChatSession::where('session_id', $response->json('session_id'))->first();
        $userMessage = $session->messages()->where('role', 'user')->first();

        $this->assertNotNull($userMessage->language_confidence);
        $this->assertIsFloat($userMessage->language_confidence);
        $this->assertGreaterThanOrEqual(0.0, $userMessage->language_confidence);
        $this->assertLessThanOrEqual(1.0, $userMessage->language_confidence);
    }
}
