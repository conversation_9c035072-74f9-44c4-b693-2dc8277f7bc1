<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DeepSeekClient
{
    private string $apiKey;
    private string $baseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.deepseek.api_key');
        $this->baseUrl = config('services.deepseek.base_url', 'https://api.deepseek.com');
        $this->timeout = config('services.deepseek.timeout', 30);
    }

    public function chat(array $messages, array $options = []): array
    {
        try {
            $payload = [
                'model' => $options['model'] ?? 'deepseek-chat',
                'messages' => $messages,
                'max_tokens' => $options['max_tokens'] ?? 2000,
                'temperature' => $options['temperature'] ?? 0.7,
                'stream' => false,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post($this->baseUrl . '/v1/chat/completions', $payload);

            if (!$response->successful()) {
                Log::error('DeepSeek API Error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                throw new \Exception('DeepSeek API request failed: ' . $response->body());
            }

            $data = $response->json();

            return [
                'success' => true,
                'content' => $data['choices'][0]['message']['content'] ?? '',
                'usage' => $data['usage'] ?? [],
                'model' => $data['model'] ?? 'deepseek-chat',
                'raw_response' => $data,
            ];

        } catch (\Exception $e) {
            Log::error('DeepSeek Client Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
            ];
        }
    }

    public function buildMessages(string $systemPrompt, string $userMessage, array $conversationHistory = []): array
    {
        $messages = [];

        // Add system message
        if (!empty($systemPrompt)) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemPrompt,
            ];
        }

        // Add conversation history
        foreach ($conversationHistory as $message) {
            $messages[] = [
                'role' => $message['role'] === 'widdx' ? 'assistant' : $message['role'],
                'content' => $message['content'],
            ];
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage,
        ];

        return $messages;
    }

    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }
}
