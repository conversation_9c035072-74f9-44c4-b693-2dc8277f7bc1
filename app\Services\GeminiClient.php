<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiClient
{
    private string $apiKey;
    private string $baseUrl;
    private int $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->baseUrl = config('services.gemini.base_url', 'https://generativelanguage.googleapis.com');
        $this->timeout = config('services.gemini.timeout', 30);
    }

    public function chat(array $messages, array $options = []): array
    {
        try {
            // Convert messages to Gemini format
            $contents = $this->convertMessagesToGeminiFormat($messages);

            $payload = [
                'contents' => $contents,
                'generationConfig' => [
                    'maxOutputTokens' => $options['max_tokens'] ?? 2000,
                    'temperature' => $options['temperature'] ?? 0.7,
                ],
            ];

            $model = $options['model'] ?? 'gemini-1.5-flash';
            $url = $this->baseUrl . '/v1beta/models/' . $model . ':generateContent?key=' . $this->apiKey;

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post($url, $payload);

            if (!$response->successful()) {
                Log::error('Gemini API Error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                throw new \Exception('Gemini API request failed: ' . $response->body());
            }

            $data = $response->json();

            return [
                'success' => true,
                'content' => $data['candidates'][0]['content']['parts'][0]['text'] ?? '',
                'model' => $model,
                'raw_response' => $data,
            ];

        } catch (\Exception $e) {
            Log::error('Gemini Client Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
            ];
        }
    }

    private function convertMessagesToGeminiFormat(array $messages): array
    {
        $contents = [];
        $systemPrompt = '';

        foreach ($messages as $message) {
            if ($message['role'] === 'system') {
                $systemPrompt = $message['content'];
                continue;
            }

            $role = $message['role'] === 'assistant' ? 'model' : 'user';
            $content = $message['content'];

            // If this is the first user message and we have a system prompt, prepend it
            if ($role === 'user' && !empty($systemPrompt) && empty($contents)) {
                $content = $systemPrompt . "\n\n" . $content;
                $systemPrompt = ''; // Clear it so we don't add it again
            }

            $contents[] = [
                'role' => $role,
                'parts' => [
                    ['text' => $content]
                ]
            ];
        }

        return $contents;
    }

    public function buildMessages(string $systemPrompt, string $userMessage, array $conversationHistory = []): array
    {
        $messages = [];

        // Add system message
        if (!empty($systemPrompt)) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemPrompt,
            ];
        }

        // Add conversation history
        foreach ($conversationHistory as $message) {
            $messages[] = [
                'role' => $message['role'] === 'widdx' ? 'assistant' : $message['role'],
                'content' => $message['content'],
            ];
        }

        // Add current user message
        $messages[] = [
            'role' => 'user',
            'content' => $userMessage,
        ];

        return $messages;
    }

    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }
}
