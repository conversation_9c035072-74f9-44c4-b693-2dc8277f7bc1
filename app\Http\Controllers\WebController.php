<?php

namespace App\Http\Controllers;

use App\Services\PersonalityModifierService;

class WebController extends Controller
{
    private PersonalityModifierService $personalityModifier;

    public function __construct(PersonalityModifierService $personalityModifier)
    {
        $this->personalityModifier = $personalityModifier;
    }

    public function index()
    {
        $personalities = $this->personalityModifier->getAvailablePersonalities();

        return view('chat', [
            'personalities' => $personalities,
        ]);
    }
}
