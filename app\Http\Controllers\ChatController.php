<?php

namespace App\Http\Controllers;

use App\Services\ContextService;
use App\Services\LanguageDetectionService;
use App\Services\ModelMergerService;
use App\Services\PersonalityModifierService;
use App\Services\SessionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    private ModelMergerService $modelMerger;
    private PersonalityModifierService $personalityModifier;
    private SessionService $sessionService;
    private ContextService $contextService;
    private LanguageDetectionService $languageDetection;

    public function __construct(
        ModelMergerService $modelMerger,
        PersonalityModifierService $personalityModifier,
        SessionService $sessionService,
        ContextService $contextService,
        LanguageDetectionService $languageDetection
    ) {
        $this->modelMerger = $modelMerger;
        $this->personalityModifier = $personalityModifier;
        $this->sessionService = $sessionService;
        $this->contextService = $contextService;
        $this->languageDetection = $languageDetection;
    }

    public function chat(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:10000',
                'session_id' => 'nullable|string|max:255',
                'personality' => 'nullable|string|in:neutral,witty,sarcastic,formal,casual',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid input',
                    'details' => $validator->errors(),
                ], 400);
            }

            $userMessage = $request->input('message');
            $sessionId = $request->input('session_id');
            $personalityType = $request->input('personality', 'neutral');

            // Detect language of user message
            $languageInfo = $this->languageDetection->detectLanguage($userMessage);

            // Get or create session
            $session = $this->sessionService->getOrCreateSession($sessionId, $personalityType);

            // Update session language preferences if needed
            $this->sessionService->updateLanguagePreferences($session, $languageInfo);

            // Add user message to session with language information
            $userMessageRecord = $this->sessionService->addMessage($session, 'user', $languageInfo['cleaned_message'], [
                'original_message' => $languageInfo['original_message'],
                'detected_language' => $languageInfo['language'],
                'detected_language_code' => $languageInfo['language_code'],
                'language_confidence' => $languageInfo['confidence'],
                'language_detection_method' => $languageInfo['method'],
                'language_override_used' => $languageInfo['method'] === 'override',
            ]);

            // Get conversation history
            $conversationHistory = $this->sessionService->getConversationHistory($session, 10);

            // Get personality prompt
            $personalityPrompt = $this->personalityModifier->getPersonalityPrompt($personalityType);

            // Process the message through our AI pipeline
            $aiResponse = $this->modelMerger->processMessage(
                $languageInfo['cleaned_message'],
                $conversationHistory,
                [
                    'personality_prompt' => $personalityPrompt,
                    'target_language' => $languageInfo['language'],
                    'target_language_code' => $languageInfo['language_code'],
                    'language_confidence' => $languageInfo['confidence'],
                    'max_tokens' => 2000,
                    'temperature' => 0.7,
                ]
            );

            if (!$aiResponse['success']) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to generate response',
                    'message' => 'I apologize, but I encountered an issue processing your request. Please try again.',
                ], 500);
            }

            // Apply personality modifications to the response
            $personalityResult = $this->personalityModifier->applyPersonality(
                $aiResponse['content'],
                $personalityType,
                $languageInfo['language']
            );

            $finalContent = $personalityResult['content'];

            // Add WIDDX response to session
            $widdxMessage = $this->sessionService->addMessage($session, 'widdx', $finalContent, [
                'processing_time' => $aiResponse['processing_time'],
                'models_used' => $aiResponse['metadata']['models_used'] ?? [],
                'personality_applied' => $personalityResult['personality_applied'],
            ]);

            // Update session context with the new interaction
            $this->contextService->updateSessionContext($session, $userMessage, $finalContent);

            return response()->json([
                'success' => true,
                'message' => $finalContent,
                'session_id' => $session->session_id,
                'personality' => $personalityResult['personality_applied'],
                'processing_time' => $aiResponse['processing_time'],
                'timestamp' => $widdxMessage->created_at->toISOString(),
            ]);

        } catch (\Exception $e) {
            Log::error('Chat Controller Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an unexpected issue. Please try again.',
            ], 500);
        }
    }

    public function getPersonalities(): JsonResponse
    {
        try {
            $personalities = $this->personalityModifier->getAvailablePersonalities();

            return response()->json([
                'success' => true,
                'personalities' => $personalities,
            ]);
        } catch (\Exception $e) {
            Log::error('Get Personalities Error', [
                'message' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch personalities',
            ], 500);
        }
    }

    public function getSessionHistory(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'session_id' => 'required|string|max:255',
                'limit' => 'nullable|integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid input',
                    'details' => $validator->errors(),
                ], 400);
            }

            $sessionId = $request->input('session_id');
            $limit = $request->input('limit', 20);

            $session = $this->sessionService->getOrCreateSession($sessionId);
            $history = $this->sessionService->getConversationHistory($session, $limit);
            $stats = $this->sessionService->getSessionStats($session);

            return response()->json([
                'success' => true,
                'session_id' => $session->session_id,
                'history' => $history,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            Log::error('Get Session History Error', [
                'message' => $e->getMessage(),
                'session_id' => $request->input('session_id'),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch session history',
            ], 500);
        }
    }

    public function updatePersonality(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'session_id' => 'required|string|max:255',
                'personality' => 'required|string|in:neutral,witty,sarcastic,formal,casual',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid input',
                    'details' => $validator->errors(),
                ], 400);
            }

            $sessionId = $request->input('session_id');
            $personalityType = $request->input('personality');

            $session = $this->sessionService->getOrCreateSession($sessionId);
            $this->sessionService->updateSessionPersonality($session, $personalityType);

            return response()->json([
                'success' => true,
                'message' => 'Personality updated successfully',
                'session_id' => $session->session_id,
                'personality' => $personalityType,
            ]);

        } catch (\Exception $e) {
            Log::error('Update Personality Error', [
                'message' => $e->getMessage(),
                'session_id' => $request->input('session_id'),
                'personality' => $request->input('personality'),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to update personality',
            ], 500);
        }
    }
}
