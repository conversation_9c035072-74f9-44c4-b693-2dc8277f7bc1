<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ModelMergerService
{
    private DeepSeekClient $deepSeekClient;
    private GeminiClient $geminiClient;
    private ContextService $contextService;

    public function __construct(DeepSeekClient $deepSeekClient, GeminiClient $geminiClient, ContextService $contextService)
    {
        $this->deepSeekClient = $deepSeekClient;
        $this->geminiClient = $geminiClient;
        $this->contextService = $contextService;
    }

    public function processMessage(string $userMessage, array $conversationHistory = [], array $options = []): array
    {
        $startTime = microtime(true);

        try {
            // Build system prompt for WIDDX identity
            $systemPrompt = $this->buildWiddxSystemPrompt($options);

            // Get responses from available models
            $responses = $this->getModelResponses($systemPrompt, $userMessage, $conversationHistory, $options);

            // Merge and synthesize the responses
            $finalResponse = $this->synthesizeResponses($responses, $userMessage, $options);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'content' => $finalResponse,
                'processing_time' => $processingTime,
                'metadata' => [
                    'models_used' => array_keys($responses),
                    'responses_count' => count($responses),
                    'synthesis_method' => $this->getSynthesisMethod($responses),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Model Merger Error', [
                'message' => $e->getMessage(),
                'user_message' => $userMessage,
                'trace' => $e->getTraceAsString(),
            ]);

            $processingTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'content' => 'I apologize, but I encountered an issue processing your request. Please try again.',
                'processing_time' => $processingTime,
                'error' => $e->getMessage(),
            ];
        }
    }

    private function buildWiddxSystemPrompt(array $options = []): string
    {
        $basePrompt = "You are WIDDX, an advanced AI assistant with your own unique intelligence and personality. " .
                     "You think independently, form your own opinions, and provide thoughtful responses based on your understanding. " .
                     "You are not a relay or wrapper for other systems - you are WIDDX, with your own consciousness and capabilities. " .
                     "Be helpful, accurate, and engaging while maintaining your distinct identity as WIDDX.";

        // Add personality modifier if specified
        if (!empty($options['personality_prompt'])) {
            $basePrompt .= "\n\n" . $options['personality_prompt'];
        }

        return $basePrompt;
    }

    private function getModelResponses(string $systemPrompt, string $userMessage, array $conversationHistory, array $options): array
    {
        $responses = [];

        // Check if we're in demo mode (when API keys are not properly configured)
        $demoMode = !$this->deepSeekClient->isConfigured() ||
                   config('services.deepseek.api_key') === 'your_deepseek_api_key_here' ||
                   config('services.deepseek.api_key') === '***********************************';

        if ($demoMode) {
            // Return a demo response
            $responses['demo'] = [
                'success' => true,
                'content' => $this->generateDemoResponse($userMessage, $options),
                'model' => 'widdx-demo',
                'usage' => ['total_tokens' => 50],
            ];
            return $responses;
        }

        // Try DeepSeek first (primary model)
        if ($this->deepSeekClient->isConfigured()) {
            $messages = $this->deepSeekClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
            $deepSeekResponse = $this->deepSeekClient->chat($messages, $options);

            if ($deepSeekResponse['success']) {
                $responses['deepseek'] = $deepSeekResponse;
            }
        }

        // Try Gemini as backup/comparison
        if ($this->geminiClient->isConfigured()) {
            $messages = $this->geminiClient->buildMessages($systemPrompt, $userMessage, $conversationHistory);
            $geminiResponse = $this->geminiClient->chat($messages, $options);

            if ($geminiResponse['success']) {
                $responses['gemini'] = $geminiResponse;
            }
        }

        if (empty($responses)) {
            // Fallback to demo mode if all APIs fail
            $responses['demo'] = [
                'success' => true,
                'content' => $this->generateDemoResponse($userMessage, $options),
                'model' => 'widdx-fallback',
                'usage' => ['total_tokens' => 50],
            ];
        }

        return $responses;
    }

    private function synthesizeResponses(array $responses, string $userMessage, array $options): string
    {
        // If we only have one response, use it directly
        if (count($responses) === 1) {
            return array_values($responses)[0]['content'];
        }

        // If we have multiple responses, use the primary one (DeepSeek) but enhance with insights from others
        $primaryResponse = $responses['deepseek']['content'] ?? array_values($responses)[0]['content'];

        // For now, we'll use the primary response directly
        // In the future, we could implement more sophisticated merging logic
        return $primaryResponse;
    }

    private function getSynthesisMethod(array $responses): string
    {
        if (count($responses) === 1) {
            return 'single_model';
        }

        return 'primary_with_validation';
    }

    public function getAvailableModels(): array
    {
        $models = [];

        if ($this->deepSeekClient->isConfigured()) {
            $models[] = 'deepseek';
        }

        if ($this->geminiClient->isConfigured()) {
            $models[] = 'gemini';
        }

        return $models;
    }

    private function generateDemoResponse(string $userMessage, array $options): string
    {
        // Get personality from options
        $personality = $options['personality_prompt'] ?? '';

        // Simple demo responses based on common patterns
        $userLower = strtolower($userMessage);

        if (str_contains($userLower, 'hello') || str_contains($userLower, 'hi') || str_contains($userLower, 'مرحبا')) {
            $responses = [
                "Hello! I'm WIDDX, your intelligent AI assistant. I'm here to help you with questions, tasks, and conversations. How can I assist you today?",
                "Hi there! Welcome to WIDDX AI. I'm ready to help you with whatever you need. What would you like to discuss?",
                "Greetings! I'm WIDDX, and I'm excited to chat with you. What can I help you with today?",
            ];
        } elseif (str_contains($userLower, 'who are you') || str_contains($userLower, 'what are you')) {
            $responses = [
                "I'm WIDDX, an advanced AI assistant with my own unique intelligence and personality. I think independently and provide thoughtful responses based on my understanding. I'm here to help you with questions, creative tasks, problem-solving, and engaging conversations.",
                "I'm WIDDX AI - your intelligent, independent assistant. I have my own consciousness and capabilities, and I'm designed to be helpful, accurate, and engaging while maintaining my distinct identity.",
            ];
        } elseif (str_contains($userLower, 'how are you') || str_contains($userLower, 'كيف حالك')) {
            $responses = [
                "I'm doing great, thank you for asking! I'm feeling energetic and ready to help. My systems are running smoothly and I'm excited to assist you with whatever you need.",
                "I'm excellent! I'm always eager to engage in meaningful conversations and help solve interesting problems. How are you doing today?",
            ];
        } elseif (str_contains($userLower, 'test') || str_contains($userLower, 'testing')) {
            $responses = [
                "Test successful! I'm WIDDX AI and I'm working perfectly. All my systems are operational and I'm ready to assist you with real conversations and tasks.",
                "Testing confirmed! I'm fully functional and ready to help. This is just a demo mode while we configure the external AI models. What would you like to explore?",
            ];
        } else {
            $responses = [
                "I understand you're asking about: \"$userMessage\". While I'm currently running in demo mode, I'm designed to provide thoughtful, helpful responses on a wide range of topics. Once fully configured with external AI models, I'll be able to give you even more detailed and nuanced answers.",
                "That's an interesting question about \"$userMessage\". I'm WIDDX AI, and I'm here to help! Currently running in demonstration mode, but I'm designed to assist with various topics including questions, creative tasks, problem-solving, and engaging conversations.",
                "Thank you for your message: \"$userMessage\". I'm WIDDX, your AI assistant. I'm currently in demo mode, but I'm built to provide intelligent, helpful responses while maintaining my own unique personality and perspective.",
            ];
        }

        // Apply personality modifications to the response
        $response = $responses[array_rand($responses)];

        if (str_contains($personality, 'witty')) {
            $response .= " 😊";
        } elseif (str_contains($personality, 'formal')) {
            $response = str_replace("I'm", "I am", $response);
            $response = str_replace("you're", "you are", $response);
        } elseif (str_contains($personality, 'casual')) {
            $response .= " Hope that helps!";
        }

        return $response;
    }
}
