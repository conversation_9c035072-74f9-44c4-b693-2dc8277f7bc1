<?php

namespace App\Console\Commands;

use App\Models\ChatSession;
use App\Models\Message;
use App\Models\Personality;
use Illuminate\Console\Command;

class TestModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widdx:test-models';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test WIDDX AI Eloquent models';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 WIDDX AI - فحص نماذج Eloquent');
        $this->line(str_repeat('=', 40));

        try {
            // Test Personality Model
            $this->info('1️⃣ اختبار نموذج Personality...');

            $personalities = Personality::all();
            $this->info("✅ تم جلب {$personalities->count()} شخصية");

            $activePersonalities = Personality::active()->get();
            $this->info("✅ الشخصيات النشطة: {$activePersonalities->count()}");

            $neutralPersonality = Personality::where('name', 'neutral')->first();
            if ($neutralPersonality) {
                $this->info("✅ شخصية neutral موجودة: {$neutralPersonality->display_name}");
            } else {
                $this->error("❌ شخصية neutral غير موجودة");
            }

            // Test ChatSession Model
            $this->info('2️⃣ اختبار نموذج ChatSession...');

            $testSession = ChatSession::create([
                'user_identifier' => 'test_user_' . time(),
                'personality_type' => 'neutral',
                'last_activity' => now(),
                'context_data' => ['test' => true]
            ]);

            $this->info("✅ تم إنشاء جلسة اختبار: {$testSession->session_id}");

            // Test Message Model
            $this->info('3️⃣ اختبار نموذج Message...');

            $userMessage = Message::create([
                'chat_session_id' => $testSession->id,
                'role' => 'user',
                'content' => 'مرحبا، هذه رسالة اختبار',
                'metadata' => ['test' => true],
                'personality_applied' => 'neutral'
            ]);

            $this->info("✅ تم إنشاء رسالة اختبار: {$userMessage->id}");

            // Test Relationships
            $this->info('4️⃣ اختبار العلاقات...');

            $sessionMessages = $testSession->messages;
            $this->info("✅ رسائل الجلسة: {$sessionMessages->count()}");

            $messageSession = $userMessage->chatSession;
            $this->info("✅ جلسة الرسالة: {$messageSession->session_id}");

            // Cleanup
            $userMessage->delete();
            $testSession->delete();
            $this->info('✅ تم تنظيف بيانات الاختبار');

            $this->info('✅ جميع اختبارات النماذج نجحت!');

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ خطأ في اختبار النماذج: " . $e->getMessage());
            return self::FAILURE;
        }
    }
}
