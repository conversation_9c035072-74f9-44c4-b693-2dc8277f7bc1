<?php

namespace App\Services;

use App\Models\Personality;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PersonalityModifierService
{
    public function applyPersonality(string $content, string $personalityName = 'neutral'): array
    {
        try {
            $personality = $this->getPersonality($personalityName);
            
            if (!$personality) {
                return [
                    'success' => true,
                    'content' => $content,
                    'personality_applied' => 'neutral',
                    'modifications' => [],
                ];
            }

            // Apply personality modifications to the content
            $modifiedContent = $this->modifyContent($content, $personality);
            
            return [
                'success' => true,
                'content' => $modifiedContent,
                'personality_applied' => $personality->name,
                'modifications' => $this->getAppliedModifications($personality),
            ];

        } catch (\Exception $e) {
            Log::error('Personality Modifier Error', [
                'message' => $e->getMessage(),
                'personality' => $personalityName,
                'content_length' => strlen($content),
            ]);

            return [
                'success' => false,
                'content' => $content, // Return original content on error
                'personality_applied' => 'neutral',
                'error' => $e->getMessage(),
            ];
        }
    }

    public function getPersonalityPrompt(string $personalityName = 'neutral'): string
    {
        $personality = $this->getPersonality($personalityName);
        
        if (!$personality) {
            return '';
        }

        return $personality->system_prompt;
    }

    public function getAvailablePersonalities(): array
    {
        return Cache::remember('personalities_list', 3600, function () {
            return Personality::active()
                ->ordered()
                ->select('name', 'display_name', 'description')
                ->get()
                ->toArray();
        });
    }

    private function getPersonality(string $name): ?Personality
    {
        return Cache::remember("personality_{$name}", 3600, function () use ($name) {
            return Personality::where('name', $name)
                ->where('is_active', true)
                ->first();
        });
    }

    private function modifyContent(string $content, Personality $personality): string
    {
        // For now, we'll return the content as-is since the personality is applied at the prompt level
        // In the future, we could implement post-processing modifications here
        
        $modifiers = $personality->response_modifiers ?? [];
        
        // Apply any post-processing modifications based on personality
        switch ($personality->name) {
            case 'witty':
                return $this->addWittyTouches($content);
            
            case 'sarcastic':
                return $this->addSarcasticTouches($content);
            
            case 'formal':
                return $this->addFormalTouches($content);
            
            case 'casual':
                return $this->addCasualTouches($content);
            
            default:
                return $content;
        }
    }

    private function addWittyTouches(string $content): string
    {
        // Add subtle witty elements if appropriate
        // This is a placeholder - in practice, you might use more sophisticated NLP
        return $content;
    }

    private function addSarcasticTouches(string $content): string
    {
        // Add subtle sarcastic elements if appropriate
        return $content;
    }

    private function addFormalTouches(string $content): string
    {
        // Ensure formal language patterns
        return $content;
    }

    private function addCasualTouches(string $content): string
    {
        // Ensure casual, friendly language patterns
        return $content;
    }

    private function getAppliedModifications(Personality $personality): array
    {
        return [
            'personality' => $personality->name,
            'tone' => $personality->response_modifiers['tone'] ?? 'neutral',
            'style' => $personality->response_modifiers['style'] ?? 'balanced',
        ];
    }

    public function validatePersonality(string $personalityName): bool
    {
        return $this->getPersonality($personalityName) !== null;
    }
}
