<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\Message;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;

class SessionService
{
    public function getOrCreateSession(string $sessionId = null, string $personalityType = 'neutral'): ChatSession
    {
        if ($sessionId) {
            $session = ChatSession::where('session_id', $sessionId)->first();
            if ($session) {
                // Update last activity
                $session->update(['last_activity' => now()]);
                return $session;
            }
        }

        // Create new session
        return ChatSession::create([
            'session_id' => $sessionId ?: Str::uuid(),
            'user_identifier' => $this->getUserIdentifier(),
            'personality_type' => $personalityType,
            'last_activity' => now(),
            'context_data' => [],
        ]);
    }

    public function addMessage(ChatSession $session, string $role, string $content, array $metadata = []): Message
    {
        $messageData = [
            'chat_session_id' => $session->id,
            'role' => $role,
            'content' => $content,
            'metadata' => $metadata,
            'personality_applied' => $session->personality_type,
        ];

        // Add language information if provided in metadata
        if (isset($metadata['detected_language'])) {
            $messageData['detected_language'] = $metadata['detected_language'];
            $messageData['detected_language_code'] = $metadata['detected_language_code'];
            $messageData['language_confidence'] = $metadata['language_confidence'];
            $messageData['language_detection_method'] = $metadata['language_detection_method'];
            $messageData['language_override_used'] = $metadata['language_override_used'];
        }

        $message = Message::create($messageData);

        // Update session last activity
        $session->update(['last_activity' => now()]);

        return $message;
    }

    public function updateLanguagePreferences(ChatSession $session, array $languageInfo): void
    {
        // Update session language preferences based on detected language
        $currentLanguageHistory = $session->language_history ?? [];

        // Track language usage
        $language = $languageInfo['language'];
        $languageCode = $languageInfo['language_code'];

        if (!isset($currentLanguageHistory[$language])) {
            $currentLanguageHistory[$language] = [
                'count' => 0,
                'first_used' => now()->toISOString(),
                'confidence_scores' => [],
            ];
        }

        $currentLanguageHistory[$language]['count']++;
        $currentLanguageHistory[$language]['last_used'] = now()->toISOString();
        $currentLanguageHistory[$language]['confidence_scores'][] = $languageInfo['confidence'];

        // Update preferred language if this language is used frequently or with high confidence
        $shouldUpdatePreferred = false;

        if ($languageInfo['method'] === 'override') {
            // User explicitly requested this language
            $shouldUpdatePreferred = true;
        } elseif ($languageInfo['confidence'] > 0.7 && $currentLanguageHistory[$language]['count'] >= 2) {
            // High confidence and used multiple times
            $shouldUpdatePreferred = true;
        } elseif ($currentLanguageHistory[$language]['count'] >= 5) {
            // Used frequently
            $shouldUpdatePreferred = true;
        }

        $updateData = ['language_history' => $currentLanguageHistory];

        if ($shouldUpdatePreferred) {
            $updateData['preferred_language'] = $language;
            $updateData['preferred_language_code'] = $languageCode;
        }

        $session->update($updateData);
    }

    public function getConversationHistory(ChatSession $session, int $limit = 10): array
    {
        return $session->getRecentMessages($limit)
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                    'created_at' => $message->created_at,
                ];
            })
            ->toArray();
    }

    public function updateSessionPersonality(ChatSession $session, string $personalityType): void
    {
        $session->update([
            'personality_type' => $personalityType,
            'last_activity' => now(),
        ]);
    }

    public function updateSessionContext(ChatSession $session, array $contextData): void
    {
        $currentContext = $session->context_data ?? [];
        $mergedContext = array_merge($currentContext, $contextData);

        $session->update([
            'context_data' => $mergedContext,
            'last_activity' => now(),
        ]);
    }

    public function cleanupOldSessions(int $daysOld = 30): int
    {
        $cutoffDate = now()->subDays($daysOld);

        return ChatSession::where('last_activity', '<', $cutoffDate)->delete();
    }

    private function getUserIdentifier(): string
    {
        // Use IP address as user identifier for anonymous users
        // In the future, this could be enhanced with user authentication
        return Request::ip() ?? 'unknown';
    }

    public function getSessionStats(ChatSession $session): array
    {
        $messageCount = $session->messages()->count();
        $userMessages = $session->messages()->where('role', 'user')->count();
        $widdxMessages = $session->messages()->where('role', 'widdx')->count();

        return [
            'total_messages' => $messageCount,
            'user_messages' => $userMessages,
            'widdx_messages' => $widdxMessages,
            'session_duration' => $session->created_at->diffInMinutes($session->last_activity),
            'personality' => $session->personality_type,
            'created_at' => $session->created_at,
            'last_activity' => $session->last_activity,
        ];
    }
}
