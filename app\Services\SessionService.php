<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\Message;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;

class SessionService
{
    public function getOrCreateSession(string $sessionId = null, string $personalityType = 'neutral'): ChatSession
    {
        if ($sessionId) {
            $session = ChatSession::where('session_id', $sessionId)->first();
            if ($session) {
                // Update last activity
                $session->update(['last_activity' => now()]);
                return $session;
            }
        }

        // Create new session
        return ChatSession::create([
            'session_id' => $sessionId ?: Str::uuid(),
            'user_identifier' => $this->getUserIdentifier(),
            'personality_type' => $personalityType,
            'last_activity' => now(),
            'context_data' => [],
        ]);
    }

    public function addMessage(ChatSession $session, string $role, string $content, array $metadata = []): Message
    {
        $message = Message::create([
            'chat_session_id' => $session->id,
            'role' => $role,
            'content' => $content,
            'metadata' => $metadata,
            'personality_applied' => $session->personality_type,
        ]);

        // Update session last activity
        $session->update(['last_activity' => now()]);

        return $message;
    }

    public function getConversationHistory(ChatSession $session, int $limit = 10): array
    {
        return $session->getRecentMessages($limit)
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                    'created_at' => $message->created_at,
                ];
            })
            ->toArray();
    }

    public function updateSessionPersonality(ChatSession $session, string $personalityType): void
    {
        $session->update([
            'personality_type' => $personalityType,
            'last_activity' => now(),
        ]);
    }

    public function updateSessionContext(ChatSession $session, array $contextData): void
    {
        $currentContext = $session->context_data ?? [];
        $mergedContext = array_merge($currentContext, $contextData);
        
        $session->update([
            'context_data' => $mergedContext,
            'last_activity' => now(),
        ]);
    }

    public function cleanupOldSessions(int $daysOld = 30): int
    {
        $cutoffDate = now()->subDays($daysOld);
        
        return ChatSession::where('last_activity', '<', $cutoffDate)->delete();
    }

    private function getUserIdentifier(): string
    {
        // Use IP address as user identifier for anonymous users
        // In the future, this could be enhanced with user authentication
        return Request::ip() ?? 'unknown';
    }

    public function getSessionStats(ChatSession $session): array
    {
        $messageCount = $session->messages()->count();
        $userMessages = $session->messages()->where('role', 'user')->count();
        $widdxMessages = $session->messages()->where('role', 'widdx')->count();
        
        return [
            'total_messages' => $messageCount,
            'user_messages' => $userMessages,
            'widdx_messages' => $widdxMessages,
            'session_duration' => $session->created_at->diffInMinutes($session->last_activity),
            'personality' => $session->personality_type,
            'created_at' => $session->created_at,
            'last_activity' => $session->last_activity,
        ];
    }
}
