<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>WIDDX AI - Intelligent Assistant</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx-primary': '#6366f1',
                        'widdx-secondary': '#8b5cf6',
                        'widdx-dark': '#0f172a',
                        'widdx-darker': '#020617',
                    }
                }
            }
        }
    </script>
    <style>
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6366f1;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .message-fade-in {
            animation: fadeInUp 0.3s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="bg-widdx-darker text-white min-h-screen">
    <div id="app" class="flex flex-col h-screen">
        <!-- Header -->
        <header class="bg-widdx-dark border-b border-gray-700 p-4">
            <div class="max-w-4xl mx-auto flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <h1 class="text-xl font-bold">WIDDX AI</h1>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Personality Selector -->
                    <select id="personality-selector" class="bg-gray-700 text-white rounded-lg px-3 py-2 text-sm border border-gray-600 focus:border-widdx-primary focus:outline-none">
                        <?php $__currentLoopData = $personalities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $personality): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($personality['name']); ?>" <?php echo e($personality['name'] === 'neutral' ? 'selected' : ''); ?>>
                                <?php echo e($personality['display_name']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    
                    <!-- Clear Chat Button -->
                    <button id="clear-chat" class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                        Clear Chat
                    </button>
                </div>
            </div>
        </header>

        <!-- Chat Messages -->
        <main class="flex-1 overflow-y-auto p-4">
            <div class="max-w-4xl mx-auto">
                <div id="messages-container" class="space-y-4">
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3 message-fade-in">
                        <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4 max-w-3xl">
                            <p class="text-gray-100">
                                Hello! I'm WIDDX, your intelligent AI assistant. I'm here to help you with questions, tasks, and conversations. 
                                You can adjust my personality using the selector above. How can I assist you today?
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Typing Indicator -->
                <div id="typing-indicator" class="hidden flex items-start space-x-3 mt-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white font-bold text-sm">W</span>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex space-x-1">
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                            <div class="typing-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Input Area -->
        <footer class="bg-widdx-dark border-t border-gray-700 p-4">
            <div class="max-w-4xl mx-auto">
                <form id="chat-form" class="flex space-x-4">
                    <div class="flex-1">
                        <textarea 
                            id="message-input" 
                            placeholder="Type your message to WIDDX..." 
                            class="w-full bg-gray-800 text-white rounded-lg px-4 py-3 border border-gray-600 focus:border-widdx-primary focus:outline-none resize-none"
                            rows="1"
                            maxlength="10000"
                        ></textarea>
                    </div>
                    <button 
                        type="submit" 
                        id="send-button"
                        class="bg-widdx-primary hover:bg-widdx-secondary text-white px-6 py-3 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Send
                    </button>
                </form>
                
                <div class="mt-2 text-xs text-gray-400 text-center">
                    WIDDX AI - Your Intelligent Assistant
                </div>
            </div>
        </footer>
    </div>

    <script>
        class WiddxChat {
            constructor() {
                this.sessionId = this.generateSessionId();
                this.currentPersonality = 'neutral';
                this.isTyping = false;
                
                this.initializeElements();
                this.bindEvents();
                this.autoResizeTextarea();
            }
            
            initializeElements() {
                this.messagesContainer = document.getElementById('messages-container');
                this.messageInput = document.getElementById('message-input');
                this.sendButton = document.getElementById('send-button');
                this.chatForm = document.getElementById('chat-form');
                this.personalitySelector = document.getElementById('personality-selector');
                this.clearChatButton = document.getElementById('clear-chat');
                this.typingIndicator = document.getElementById('typing-indicator');
            }
            
            bindEvents() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.personalitySelector.addEventListener('change', (e) => this.handlePersonalityChange(e));
                this.clearChatButton.addEventListener('click', () => this.clearChat());
                this.messageInput.addEventListener('keydown', (e) => this.handleKeydown(e));
            }
            
            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }
            
            autoResizeTextarea() {
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
                });
            }
            
            handleKeydown(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.chatForm.dispatchEvent(new Event('submit'));
                }
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const message = this.messageInput.value.trim();
                if (!message || this.isTyping) return;
                
                this.addUserMessage(message);
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                this.setTyping(true);
                
                try {
                    const response = await this.sendMessage(message);
                    this.addWiddxMessage(response.message);
                } catch (error) {
                    this.addWiddxMessage('I apologize, but I encountered an issue processing your request. Please try again.');
                    console.error('Chat error:', error);
                } finally {
                    this.setTyping(false);
                }
            }
            
            async sendMessage(message) {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: this.sessionId,
                        personality: this.currentPersonality
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                return await response.json();
            }
            
            addUserMessage(message) {
                const messageElement = this.createMessageElement('user', message);
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }
            
            addWiddxMessage(message) {
                const messageElement = this.createMessageElement('widdx', message);
                this.messagesContainer.appendChild(messageElement);
                this.scrollToBottom();
            }
            
            createMessageElement(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex items-start space-x-3 message-fade-in';
                
                if (role === 'user') {
                    messageDiv.className += ' flex-row-reverse space-x-reverse';
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">U</span>
                        </div>
                        <div class="bg-widdx-primary rounded-lg p-4 max-w-3xl">
                            <p class="text-white">${this.escapeHtml(content)}</p>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-r from-widdx-primary to-widdx-secondary rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4 max-w-3xl">
                            <p class="text-gray-100">${this.escapeHtml(content)}</p>
                        </div>
                    `;
                }
                
                return messageDiv;
            }
            
            setTyping(isTyping) {
                this.isTyping = isTyping;
                this.sendButton.disabled = isTyping;
                this.messageInput.disabled = isTyping;
                
                if (isTyping) {
                    this.typingIndicator.classList.remove('hidden');
                    this.scrollToBottom();
                } else {
                    this.typingIndicator.classList.add('hidden');
                }
            }
            
            handlePersonalityChange(e) {
                this.currentPersonality = e.target.value;
                // Optionally show a message about personality change
                console.log('Personality changed to:', this.currentPersonality);
            }
            
            clearChat() {
                // Remove all messages except the welcome message
                const messages = this.messagesContainer.children;
                for (let i = messages.length - 1; i > 0; i--) {
                    messages[i].remove();
                }
                
                // Generate new session ID
                this.sessionId = this.generateSessionId();
            }
            
            scrollToBottom() {
                setTimeout(() => {
                    window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: 'smooth'
                    });
                }, 100);
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }
        
        // Initialize the chat when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new WiddxChat();
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\widdx-ai\resources\views/chat.blade.php ENDPATH**/ ?>