<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Message extends Model
{
    protected $fillable = [
        'chat_session_id',
        'role',
        'content',
        'metadata',
        'personality_applied',
        'processing_time',
    ];

    protected $casts = [
        'metadata' => 'array',
        'processing_time' => 'float',
    ];

    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    public function isFromUser(): bool
    {
        return $this->role === 'user';
    }

    public function isFromWiddx(): bool
    {
        return $this->role === 'widdx';
    }
}
