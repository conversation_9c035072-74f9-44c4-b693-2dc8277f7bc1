<?php

return [

    /*
    |--------------------------------------------------------------------------
    | WIDDX AI Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options specific to WIDDX AI.
    | These settings control the behavior and limits of the AI assistant.
    |
    */

    'default_personality' => env('WIDDX_DEFAULT_PERSONALITY', 'neutral'),

    'max_message_length' => env('WIDDX_MAX_MESSAGE_LENGTH', 10000),

    'session_cleanup_days' => env('WIDDX_SESSION_CLEANUP_DAYS', 30),

    'enable_logging' => env('WIDDX_ENABLE_LOGGING', true),

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for API requests to prevent abuse.
    |
    */

    'rate_limiting' => [
        'enabled' => env('WIDDX_RATE_LIMITING_ENABLED', true),
        'max_requests_per_minute' => env('WIDDX_MAX_REQUESTS_PER_MINUTE', 60),
        'max_requests_per_hour' => env('WIDDX_MAX_REQUESTS_PER_HOUR', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Response Settings
    |--------------------------------------------------------------------------
    |
    | Configure default response behavior and limits.
    |
    */

    'response' => [
        'max_tokens' => env('WIDDX_MAX_TOKENS', 2000),
        'default_temperature' => env('WIDDX_DEFAULT_TEMPERATURE', 0.7),
        'timeout_seconds' => env('WIDDX_TIMEOUT_SECONDS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security-related settings for WIDDX AI.
    |
    */

    'security' => [
        'content_filtering' => env('WIDDX_CONTENT_FILTERING', true),
        'log_user_messages' => env('WIDDX_LOG_USER_MESSAGES', true),
        'anonymize_logs' => env('WIDDX_ANONYMIZE_LOGS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Model Configuration
    |--------------------------------------------------------------------------
    |
    | Configure which AI models to use and their priorities.
    |
    */

    'models' => [
        'primary' => env('WIDDX_PRIMARY_MODEL', 'deepseek'),
        'fallback' => env('WIDDX_FALLBACK_MODEL', 'gemini'),
        'enable_model_comparison' => env('WIDDX_ENABLE_MODEL_COMPARISON', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Personality System
    |--------------------------------------------------------------------------
    |
    | Configure the personality system behavior.
    |
    */

    'personalities' => [
        'allow_custom' => env('WIDDX_ALLOW_CUSTOM_PERSONALITIES', false),
        'cache_duration' => env('WIDDX_PERSONALITY_CACHE_DURATION', 3600), // seconds
    ],

];
