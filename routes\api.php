<?php

use App\Http\Controllers\ChatController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WIDDX AI Chat Routes
Route::prefix('chat')->middleware('widdx.rate_limit')->group(function () {
    Route::post('/', [ChatController::class, 'chat']);
    Route::get('/personalities', [ChatController::class, 'getPersonalities']);
    Route::get('/history', [ChatController::class, 'getSessionHistory']);
    Route::put('/personality', [ChatController::class, 'updatePersonality']);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'service' => 'WIDDX AI',
        'timestamp' => now()->toISOString(),
    ]);
});
